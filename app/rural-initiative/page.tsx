import { Metadata } from 'next';
import { Suspense } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import CacheManager from '@/components/cache/CacheManager';
import { PageErrorBoundary } from '@/components/error/ComprehensiveErrorBoundary';
import UdbhavClient from '@/components/rural-initiative/UdbhavClient';
import { UDBHAV_INFO } from '@/lib/constants';

export const metadata: Metadata = {
  title: 'Udbhav Initiative | Positive7 Educational Tours',
  description: 'Experience authentic rural India through Udbhav initiative. Immersive cultural exchanges, traditional arts, sustainable living practices, and meaningful connections with rural communities.',
  keywords: 'rural initiative, cultural exchange, traditional arts, sustainable living, village homestays, rural tourism, educational programs, Udbhav, Positive7',
  openGraph: {
    title: 'Udbhav Initiative | Positive7',
    description: 'Bridge the gap between urban and rural India through authentic cultural experiences and educational programs.',
    images: [
      {
        url: UDBHAV_INFO.images[0],
        width: 1200,
        height: 630,
        alt: 'Udbhav Initiative'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Udbhav Initiative | Positive7',
    description: 'Experience authentic rural India through immersive cultural exchanges and traditional arts.',
    images: [UDBHAV_INFO.images[0]]
  }
};

// Loading component for Suspense fallback
function UdbhavPageLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading Udbhav Initiative...</p>
      </div>
    </div>
  );
}

export default function UdbhavPage() {
  return (
    <PageErrorBoundary context="udbhav-page">
      {/* Cache management for dynamic content */}
      <CacheManager addMetaTags={true} updateSW={true} />

      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
          <Suspense fallback={<UdbhavPageLoading />}>
            <UdbhavClient />
          </Suspense>
        </div>
      </main>
      <Footer />
    </PageErrorBoundary>
  );
}
