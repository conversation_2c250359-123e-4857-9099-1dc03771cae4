'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ChevronDown,
  ChevronUp
} from 'lucide-react'

const FAQS = [
  {
    category: 'General',
    questions: [
      {
        question: 'What makes Positive7 different from other tour operators?',
        answer: 'Positive7 specializes exclusively in educational tours with a focus on experiential learning. We have 15+ years of experience, curriculum-aligned programs, trained educational guides, and a perfect safety record. Our trips are designed to be transformative learning experiences, not just sightseeing tours.'
      },
      {
        question: 'What age groups do you cater to?',
        answer: 'We design programs for students from Grade 6 to Grade 12 (ages 12-18). Our itineraries are age-appropriate and include activities that match the physical and intellectual capabilities of different age groups.'
      },
      {
        question: 'Do you offer customized trips?',
        answer: 'Yes! We specialize in creating customized educational tours based on your specific curriculum requirements, budget, duration, and learning objectives. Our team works closely with educators to design meaningful experiences.'
      }
    ]
  },
  {
    category: 'Booking & Pricing',
    questions: [
      {
        question: 'How far in advance should we book?',
        answer: 'We recommend booking at least 2-3 months in advance, especially for peak seasons (October-March). This ensures better accommodation options, confirmed transportation, and adequate time for pre-trip preparations.'
      },
      {
        question: 'What is included in the trip cost?',
        answer: 'Our packages typically include accommodation, all meals, transportation, entry fees, activity costs, professional guides, travel insurance, and 24/7 support. Detailed inclusions and exclusions are provided with each quote.'
      },
      {
        question: 'What are the payment terms?',
        answer: 'We typically require 50% advance payment at the time of booking and the balance 15 days before departure. We accept payments through bank transfer, cheques, and online payment gateways.'
      }
    ]
  },
  {
    category: 'Safety & Support',
    questions: [
      {
        question: 'What safety measures do you have in place?',
        answer: 'Safety is our top priority. We have trained guides, first-aid certified staff, 24/7 support team, comprehensive travel insurance, verified accommodations, safe transportation, and detailed emergency protocols. We maintain a perfect safety record over 15+ years.'
      },
      {
        question: 'How do you handle medical emergencies?',
        answer: 'All our guides are first-aid trained, we carry medical kits, have tie-ups with local hospitals at destinations, maintain emergency contact lists, and have 24/7 support for immediate assistance. Parents are informed immediately of any medical situations.'
      },
      {
        question: 'What is your student-to-guide ratio?',
        answer: 'We maintain a ratio of 1 guide for every 15-20 students, ensuring adequate supervision and personalized attention. For adventure activities, this ratio is even lower for enhanced safety.'
      }
    ]
  },
  {
    category: 'Trip Details',
    questions: [
      {
        question: 'What should students pack for the trip?',
        answer: 'We provide a detailed packing list 2 weeks before departure, customized for the destination and season. This includes clothing recommendations, essential items, and things to avoid bringing.'
      },
      {
        question: 'How do you handle dietary restrictions?',
        answer: 'We accommodate all dietary requirements including vegetarian, vegan, Jain, gluten-free, and other special needs. Please inform us at the time of booking so we can make appropriate arrangements.'
      },
      {
        question: 'Can parents track the trip progress?',
        answer: 'Yes! We provide regular updates through WhatsApp groups, photo sharing, and daily progress reports. Parents receive real-time information about activities, meals, and their child\'s well-being.'
      }
    ]
  }
]

export function FAQSection() {
  const [activeCategory, setActiveCategory] = useState('General')
  const [expandedQuestion, setExpandedQuestion] = useState<number | null>(null)

  const toggleQuestion = (index: number) => {
    setExpandedQuestion(expandedQuestion === index ? null : index)
  }

  const activeFAQs = FAQS.find(category => category.category === activeCategory)?.questions || []

  return (
    <section className="py-20">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Find answers to common questions about our educational tours. 
            Can't find what you're looking for? Contact us directly.
          </p>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Category Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl p-6 shadow-lg sticky top-24">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Categories</h3>
              <div className="space-y-2">
                {FAQS.map((category) => (
                  <button
                    key={category.category}
                    onClick={() => {
                      setActiveCategory(category.category)
                      setExpandedQuestion(null)
                    }}
                    className={`w-full text-left px-4 py-3 rounded-lg transition-colors ${
                      activeCategory === category.category
                        ? 'bg-blue-100 text-blue-700 font-medium'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {category.category}
                    <span className="text-sm text-gray-500 ml-2">
                      ({category.questions.length})
                    </span>
                  </button>
                ))}
              </div>


            </div>
          </div>

          {/* FAQ Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">{activeCategory}</h3>
              
              <div className="space-y-4">
                {activeFAQs.map((faq, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="border border-gray-200 rounded-lg overflow-hidden"
                  >
                    <button
                      onClick={() => toggleQuestion(index)}
                      className="w-full p-6 text-left bg-gray-50 hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <h4 className="text-lg font-medium text-gray-900 pr-4">
                          {faq.question}
                        </h4>
                        <div className="flex-shrink-0">
                          {expandedQuestion === index ? (
                            <ChevronUp className="w-5 h-5 text-gray-500" />
                          ) : (
                            <ChevronDown className="w-5 h-5 text-gray-500" />
                          )}
                        </div>
                      </div>
                    </button>
                    
                    <AnimatePresence>
                      {expandedQuestion === index && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3 }}
                          className="overflow-hidden"
                        >
                          <div className="p-6 pt-0">
                            <p className="text-gray-700 leading-relaxed">
                              {faq.answer}
                            </p>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                ))}
              </div>
            </div>


          </div>
        </div>
      </div>
    </section>
  )
}
